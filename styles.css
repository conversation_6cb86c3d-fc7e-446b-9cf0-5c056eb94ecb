/* OmniWord 划词翻译插件样式 */

/* 翻译按钮样式 */
.omniword-translate-button {
  position: absolute !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  padding: 8px !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  z-index: 999999 !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 36px !important;
  height: 36px !important;
  border: none !important;
  outline: none !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
}

.omniword-translate-button:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6) !important;
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
}

.omniword-translate-button:active {
  transform: scale(0.95) !important;
}

.omniword-translate-button svg {
  width: 18px !important;
  height: 18px !important;
  fill: white !important;
}

/* 翻译弹窗样式 */
.omniword-translate-popup {
  position: absolute !important;
  background: white !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 12px !important;
  padding: 20px !important;
  min-width: 280px !important;
  max-width: 450px !important;
  z-index: 1000000 !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
  animation: omniwordFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.omniword-translate-popup * {
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 关闭按钮 */
.omniword-close-btn {
  position: absolute !important;
  top: 8px !important;
  right: 12px !important;
  font-size: 24px !important;
  color: #a0aec0 !important;
  cursor: pointer !important;
  width: 28px !important;
  height: 28px !important;
  text-align: center !important;
  line-height: 28px !important;
  border-radius: 50% !important;
  transition: all 0.2s ease !important;
  font-weight: bold !important;
}

.omniword-close-btn:hover {
  background: #f7fafc !important;
  color: #4a5568 !important;
}

/* 单词标题 */
.omniword-word-title {
  margin: 0 0 16px 0 !important;
  color: #2d3748 !important;
  font-size: 24px !important;
  font-weight: 600 !important;
  padding-right: 30px !important;
  line-height: 1.2 !important;
}

/* 星级评分 */
.omniword-stars {
  display: inline-flex !important;
  margin-left: 8px !important;
  align-items: center !important;
}

.omniword-star {
  width: 14px !important;
  height: 14px !important;
  margin-right: 1px !important;
}

/* 音标区域 */
.omniword-phonetics {
  margin-bottom: 16px !important;
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 16px !important;
}

.omniword-phonetic-item {
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

.omniword-phonetic-type {
  color: #718096 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.omniword-phonetic-text {
  color: #4a5568 !important;
  font-size: 16px !important;
  font-family: "Times New Roman", serif !important;
}

.omniword-audio-btn {
  background: none !important;
  border: none !important;
  cursor: pointer !important;
  padding: 4px !important;
  border-radius: 4px !important;
  transition: background 0.2s ease !important;
  font-size: 16px !important;
}

.omniword-audio-btn:hover {
  background: #edf2f7 !important;
}

/* 翻译内容 */
.omniword-translations {
  border-top: 1px solid #e2e8f0 !important;
  padding-top: 16px !important;
  margin-bottom: 16px !important;
}

.omniword-translation-item {
  margin: 8px 0 !important;
  color: #2d3748 !important;
  line-height: 1.6 !important;
  font-size: 15px !important;
}

/* 例句区域 */
.omniword-examples {
  border-top: 1px solid #e2e8f0 !important;
  padding-top: 16px !important;
}

.omniword-examples-title {
  color: #4a5568 !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  margin-bottom: 12px !important;
}

.omniword-example-item {
  margin-bottom: 12px !important;
  padding: 12px !important;
  background: #f7fafc !important;
  border-radius: 8px !important;
  border-left: 3px solid #667eea !important;
}

.omniword-example-en {
  color: #2d3748 !important;
  margin-bottom: 6px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.omniword-example-cn {
  color: #718096 !important;
  font-size: 13px !important;
  line-height: 1.4 !important;
}

/* 加载状态 */
.omniword-loading {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 20px !important;
  color: #718096 !important;
}

.omniword-loading-spinner {
  width: 20px !important;
  height: 20px !important;
  border: 2px solid #e2e8f0 !important;
  border-top: 2px solid #667eea !important;
  border-radius: 50% !important;
  animation: omniwordSpin 1s linear infinite !important;
  margin-right: 8px !important;
}

/* 错误状态 */
.omniword-error {
  color: #e53e3e !important;
  padding: 16px !important;
  text-align: center !important;
  font-size: 14px !important;
}

/* 动画 */
@keyframes omniwordFadeIn {
  from {
    opacity: 0 !important;
    transform: translateY(-10px) scale(0.95) !important;
  }
  to {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
  }
}

@keyframes omniwordSpin {
  0% { transform: rotate(0deg) !important; }
  100% { transform: rotate(360deg) !important; }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .omniword-translate-popup {
    max-width: 90vw !important;
    margin: 0 5vw !important;
  }
}

/* 确保在所有网站上都能正确显示 */
.omniword-translate-popup,
.omniword-translate-popup *,
.omniword-translate-button,
.omniword-translate-button * {
  all: unset !important;
}

/* 重新应用必要的样式 */
.omniword-translate-popup {
  display: block !important;
  position: absolute !important;
  background: white !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 12px !important;
  padding: 20px !important;
  min-width: 280px !important;
  max-width: 450px !important;
  z-index: 1000000 !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
  animation: omniwordFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}
