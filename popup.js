// popup.js - OmniWord弹窗界面脚本

document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.getElementById('searchInput');
  const searchBtn = document.getElementById('searchBtn');
  const resultContainer = document.getElementById('resultContainer');
  const loading = document.getElementById('loading');
  const error = document.getElementById('error');
  const result = document.getElementById('result');

  // 搜索按钮点击事件
  searchBtn.addEventListener('click', handleSearch);
  
  // 输入框回车事件
  searchInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      handleSearch();
    }
  });

  // 自动聚焦到输入框
  searchInput.focus();

  // 处理搜索
  async function handleSearch() {
    const word = searchInput.value.trim();
    if (!word) {
      showError('请输入要翻译的内容');
      return;
    }

    showLoading();
    
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'translate',
        word: word
      });

      if (response && response.success) {
        showResult(response.data);
      } else {
        showError(response?.error || '翻译失败，请重试');
      }
    } catch (err) {
      showError('网络错误，请检查网络连接');
    }
  }

  // 显示加载状态
  function showLoading() {
    loading.classList.remove('hidden');
    error.classList.add('hidden');
    result.innerHTML = '';
    searchBtn.disabled = true;
    searchBtn.textContent = '翻译中...';
  }

  // 显示错误
  function showError(message) {
    loading.classList.add('hidden');
    error.classList.remove('hidden');
    error.textContent = message;
    result.innerHTML = '';
    searchBtn.disabled = false;
    searchBtn.textContent = '翻译';
  }

  // 显示结果
  function showResult(data) {
    loading.classList.add('hidden');
    error.classList.add('hidden');
    searchBtn.disabled = false;
    searchBtn.textContent = '翻译';

    if (!data || (!data.word && !data.translations?.length)) {
      showError('未找到翻译结果');
      return;
    }

    result.innerHTML = formatResult(data);
  }

  // 格式化翻译结果
  function formatResult(data) {
    let html = '';

    // 单词标题
    if (data.word) {
      html += `<div class="word-title">${escapeHtml(data.word)}</div>`;
    }

    // 音标
    if (data.phonetics && data.phonetics.length > 0) {
      html += '<div class="phonetics">';
      data.phonetics.forEach(p => {
        html += `
          <div class="phonetic-item">
            <span class="phonetic-type">${escapeHtml(p.type)}</span>
            <span class="phonetic-text">${escapeHtml(p.text)}</span>
            ${p.audio ? `
              <button class="audio-btn" onclick="playAudio('${p.audio}')" title="播放发音">
                🔊
              </button>
            ` : ''}
          </div>
        `;
      });
      html += '</div>';
    }

    // 翻译
    if (data.translations && data.translations.length > 0) {
      html += '<div class="translations">';
      data.translations.forEach(t => {
        html += `<div class="translation-item">${escapeHtml(t)}</div>`;
      });
      html += '</div>';
    }

    // 例句
    if (data.examples && data.examples.length > 0) {
      html += `
        <div class="examples">
          <div class="examples-title">例句：</div>
      `;
      data.examples.forEach(ex => {
        html += `
          <div class="example-item">
            <div class="example-en">${escapeHtml(ex.en)}</div>
            <div class="example-cn">${escapeHtml(ex.cn)}</div>
          </div>
        `;
      });
      html += '</div>';
    }

    return html;
  }

  // HTML转义
  function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  // 播放音频
  window.playAudio = function(url) {
    try {
      const audio = new Audio(url);
      audio.play().catch(err => {
        console.warn('音频播放失败:', err);
      });
    } catch (err) {
      console.warn('音频播放失败:', err);
    }
  };

  // 从存储中恢复上次的搜索内容
  chrome.storage.local.get(['lastSearch'], function(result) {
    if (result.lastSearch) {
      searchInput.value = result.lastSearch;
    }
  });

  // 保存搜索内容
  searchInput.addEventListener('input', function() {
    chrome.storage.local.set({ lastSearch: searchInput.value });
  });
});
