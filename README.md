# OmniWord - Chrome 划词翻译插件

OmniWord 是一款功能强大的 Chrome 浏览器划词翻译插件，旨在为您提供无缝、高效的网页词语和句子翻译体验。
参考项目：https://deepwiki.com/search/_060a6e5d-a59d-4db8-893c-4f93f3bb4177

## 当前功能

- **点击图标查词**: 用户可以点击浏览器工具栏中的 OmniWord 插件图标，在弹出的窗口中输入单词或句子进行快速查询。
- **划词即时翻译**: 在任何网页上，只需用鼠标选中您想要翻译的单词或句子，OmniWord 就会立即显示翻译结果。此功能类似于广受欢迎的沙拉查词（Saladict）插件，旨在提供流畅的在页翻译体验。

**当前翻译源**: 目前，本插件的翻译结果是通过抓取并解析有道词典的网页内容来实现的。

## 未来计划

我们致力于将 OmniWord 打造成一个更智能、更强大的翻译工具。未来的开发重点包括：

- **AI 驱动的翻译**: 我们计划集成先进的 AI 技术，通过接入 OpenAI 等大语言模型 API，为您提供更精准、更自然、更具上下文感知能力的翻译结果。
- **更多翻译源支持**: 增加更多可靠的翻译服务提供商，让用户可以根据自己的需求选择最合适的翻译引擎。
- **用户自定义功能**: 提供更丰富的自定义选项，例如个性化的界面主题、快捷键设置等。

## 如何安装和使用

1.  克隆或下载此仓库到您的本地计算机。
2.  在项目根目录下运行 `npm install` 来安装项目依赖。
3.  运行 `npx webpack` 来编译 TypeScript 文件。
4.  打开 Chrome 浏览器，进入 `chrome://extensions` 页面。
5.  启用“开发者模式”。
6.  点击“加载已解压的扩展程序”，然后选择本项目的 `OmniWord` 文件夹。

现在，您应该可以在浏览器工具栏中看到 OmniWord 的图标了。