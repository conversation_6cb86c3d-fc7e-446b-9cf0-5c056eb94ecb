<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OmniWord 翻译</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      width: 380px;
      min-height: 500px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
    }

    .header {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .logo {
      color: white;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .subtitle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
    }

    .search-container {
      padding: 20px;
      background: white;
    }

    .search-box {
      position: relative;
      margin-bottom: 16px;
    }

    .search-input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 16px;
      outline: none;
      transition: border-color 0.2s ease;
    }

    .search-input:focus {
      border-color: #667eea;
    }

    .search-btn {
      width: 100%;
      padding: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: transform 0.2s ease;
    }

    .search-btn:hover {
      transform: translateY(-1px);
    }

    .search-btn:active {
      transform: translateY(0);
    }

    .search-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .result-container {
      max-height: 300px;
      overflow-y: auto;
      padding: 0 20px 20px;
      background: white;
    }

    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #718096;
    }

    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid #e2e8f0;
      border-top: 2px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .error {
      color: #e53e3e;
      text-align: center;
      padding: 20px;
      font-size: 14px;
    }

    .word-title {
      font-size: 20px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 12px;
    }

    .phonetics {
      margin-bottom: 16px;
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }

    .phonetic-item {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .phonetic-type {
      color: #718096;
      font-size: 12px;
      font-weight: 500;
    }

    .phonetic-text {
      color: #4a5568;
      font-size: 14px;
      font-family: "Times New Roman", serif;
    }

    .audio-btn {
      background: none;
      border: none;
      cursor: pointer;
      padding: 2px;
      border-radius: 4px;
      transition: background 0.2s ease;
      font-size: 14px;
    }

    .audio-btn:hover {
      background: #edf2f7;
    }

    .translations {
      border-top: 1px solid #e2e8f0;
      padding-top: 12px;
      margin-bottom: 16px;
    }

    .translation-item {
      margin: 6px 0;
      color: #2d3748;
      line-height: 1.5;
      font-size: 14px;
    }

    .examples {
      border-top: 1px solid #e2e8f0;
      padding-top: 12px;
    }

    .examples-title {
      color: #4a5568;
      font-size: 12px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .example-item {
      margin-bottom: 8px;
      padding: 8px;
      background: #f7fafc;
      border-radius: 6px;
      border-left: 3px solid #667eea;
    }

    .example-en {
      color: #2d3748;
      margin-bottom: 4px;
      font-size: 12px;
      line-height: 1.4;
    }

    .example-cn {
      color: #718096;
      font-size: 11px;
      line-height: 1.3;
    }

    .footer {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      padding: 12px 20px;
      text-align: center;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    .footer-text {
      color: rgba(255, 255, 255, 0.7);
      font-size: 12px;
    }

    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">OmniWord</div>
    <div class="subtitle">智能划词翻译</div>
  </div>

  <div class="search-container">
    <div class="search-box">
      <input type="text" class="search-input" id="searchInput" placeholder="输入单词或句子进行翻译..." autocomplete="off">
    </div>
    <button class="search-btn" id="searchBtn">翻译</button>
  </div>

  <div class="result-container" id="resultContainer">
    <div class="loading hidden" id="loading">
      <div class="loading-spinner"></div>
      <span>翻译中...</span>
    </div>
    <div class="error hidden" id="error"></div>
    <div id="result"></div>
  </div>

  <div class="footer">
    <div class="footer-text">支持网页划词翻译</div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
