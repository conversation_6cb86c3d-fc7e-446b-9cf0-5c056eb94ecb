// background.js - OmniWord翻译引擎
const HOST = 'http://www.youdao.com';

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'translate') {
    handleTranslate(request.word)
      .then(result => sendResponse({ success: true, data: result }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true;
  }
});

// 获取源页面URL
function getSrcPage(text) {
  return 'https://dict.youdao.com/w/' + encodeURIComponent(text.replace(/\s+/g, ' '));
}

// 主翻译函数
async function handleTranslate(word) {
  const url = getSrcPage(word.trim());

  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();
    return parseYoudaoResult(html);
  } catch (error) {
    throw new Error('网络请求失败: ' + error.message);
  }
}

// 解析有道翻译结果
function parseYoudaoResult(html) {
  const result = {
    word: '',
    phonetics: [],
    translations: [],
    examples: []
  };

  // 检查是否有拼写建议
  const typoMatch = html.match(/<div class="error-typo"[^>]*>([\s\S]*?)<\/div>/);
  if (typoMatch) {
    result.suggestion = typoMatch[1].replace(/<[^>]+>/g, '').trim();
    return result;
  }

  // 提取单词标题
  const keywordMatch = html.match(/<span class="keyword">([^<]+)<\/span>/) ||
                      html.match(/<h1[^>]*class="keyword"[^>]*>([^<]+)<\/h1>/) ||
                      html.match(/<div[^>]*class="keyword"[^>]*>([^<]+)<\/div>/);
  if (keywordMatch) {
    result.word = keywordMatch[1].trim();
  }

  // 提取星级评分
  const starMatch = html.match(/<span class="star\s+star(\d+)"/);
  if (starMatch) {
    result.stars = parseInt(starMatch[1]);
  }

  // 提取词汇等级
  const rankMatch = html.match(/<span class="rank">([^<]+)<\/span>/);
  if (rankMatch) {
    result.rank = rankMatch[1].trim();
  }

  // 提取音标和发音
  const pronounceRegex = /<div class="baav"[\s\S]*?<\/div>/;
  const pronounceSection = html.match(pronounceRegex);
  if (pronounceSection) {
    const pronounceHTML = pronounceSection[0];
    const phoneticRegex = /<span class="phonetic">([^<]+)<\/span>/g;
    const voiceRegex = /<a[^>]*class="dictvoice"[^>]*data-rel="([^"]+)"[^>]*>/g;

    let phoneticMatch;
    const phonetics = [];
    const audioUrls = [];

    // 提取音标
    while ((phoneticMatch = phoneticRegex.exec(pronounceHTML)) !== null) {
      phonetics.push(phoneticMatch[1].trim());
    }

    // 提取音频链接
    let voiceMatch;
    while ((voiceMatch = voiceRegex.exec(pronounceHTML)) !== null) {
      audioUrls.push(`https://dict.youdao.com/dictvoice?audio=${voiceMatch[1]}`);
    }

    // 组合音标和音频
    if (phonetics.length > 0) {
      phonetics.forEach((phonetic, index) => {
        const type = index === 0 ? '英' : '美';
        result.phonetics.push({
          type: type,
          text: phonetic,
          audio: audioUrls[index] || null
        });
      });
    }
  }

  // 提取基本释义
  const transContainerRegex = /<div[^>]*class="trans-container"[^>]*>([\s\S]*?)<\/div>/;
  const transMatch = html.match(transContainerRegex);
  if (transMatch) {
    const transHTML = transMatch[1];

    // 尝试提取列表形式的释义
    const ulMatch = transHTML.match(/<ul[^>]*>([\s\S]*?)<\/ul>/);
    if (ulMatch) {
      const liRegex = /<li[^>]*>([^<]*(?:<[^>]+>[^<]*)*)<\/li>/g;
      let liMatch;
      while ((liMatch = liRegex.exec(ulMatch[1])) !== null) {
        const text = liMatch[1].replace(/<[^>]+>/g, '').trim();
        if (text && text.length > 0) {
          result.translations.push(text);
        }
      }
    }

    // 如果没有找到列表，尝试提取段落形式的释义
    if (result.translations.length === 0) {
      const pRegex = /<p[^>]*>([^<]*(?:<[^>]+>[^<]*)*)<\/p>/g;
      let pMatch;
      while ((pMatch = pRegex.exec(transHTML)) !== null) {
        const text = pMatch[1].replace(/<[^>]+>/g, '').trim();
        if (text && text.length > 0) {
          result.translations.push(text);
        }
      }
    }

    // 最后尝试提取所有文本内容
    if (result.translations.length === 0) {
      const cleanText = transHTML.replace(/<[^>]+>/g, ' ').replace(/\s+/g, ' ').trim();
      if (cleanText && cleanText.length > 0) {
        result.translations.push(cleanText);
      }
    }
  }

  // 提取例句
  const exampleSectionRegex = /<div[^>]*class="[^"]*authority[^"]*"[^>]*>([\s\S]*?)<\/div>/;
  const exampleSection = html.match(exampleSectionRegex);
  if (exampleSection) {
    const exampleHTML = exampleSection[1];
    const sentenceRegex = /<p[^>]*class="[^"]*example[^"]*"[^>]*>([^<]+)<\/p>[\s\S]*?<p[^>]*class="[^"]*example[^"]*"[^>]*>([^<]+)<\/p>/g;
    let sentenceMatch;
    let exampleCount = 0;

    while ((sentenceMatch = sentenceRegex.exec(exampleHTML)) !== null && exampleCount < 5) {
      result.examples.push({
        en: sentenceMatch[1].trim(),
        cn: sentenceMatch[2].trim()
      });
      exampleCount++;
    }
  }

  // 如果没有找到例句，尝试其他格式
  if (result.examples.length === 0) {
    const altExampleRegex = /<div[^>]*class="[^"]*bilingual[^"]*"[^>]*>[\s\S]*?<p[^>]*>([^<]+)<\/p>[\s\S]*?<p[^>]*>([^<]+)<\/p>/g;
    let altMatch;
    let count = 0;
    while ((altMatch = altExampleRegex.exec(html)) !== null && count < 3) {
      result.examples.push({
        en: altMatch[1].trim(),
        cn: altMatch[2].trim()
      });
      count++;
    }
  }

  // 添加柯林斯词典信息
  const collinsRegex = /<div[^>]*id="collinsResult"[^>]*>([\s\S]*?)<\/div>/;
  const collinsMatch = html.match(collinsRegex);
  if (collinsMatch) {
    result.collins = extractCollinsInfo(collinsMatch[1]);
  }

  return result;
}

// 提取柯林斯词典信息
function extractCollinsInfo(collinsHTML) {
  const collins = [];
  const containerRegex = /<div[^>]*class="wt-container"[^>]*>([\s\S]*?)<\/div>/g;
  let containerMatch;

  while ((containerMatch = containerRegex.exec(collinsHTML)) !== null) {
    const containerContent = containerMatch[1];
    const titleMatch = containerContent.match(/<div[^>]*class="title[^"]*"[^>]*>([^<]+)<\/div>/);
    const contentMatch = containerContent.match(/<div[^>]*class="content[^"]*"[^>]*>([\s\S]*?)<\/div>/);

    if (titleMatch && contentMatch) {
      collins.push({
        title: titleMatch[1].trim(),
        content: contentMatch[1].replace(/<[^>]+>/g, '').trim()
      });
    }
  }

  return collins;
}

