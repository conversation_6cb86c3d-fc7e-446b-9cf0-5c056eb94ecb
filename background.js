// background.js - OmniWord翻译引擎
let creating; // 防止重复创建offscreen document

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'translate') {
    handleTranslate(request.word)
      .then(result => sendResponse({ success: true, data: result }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true;
  }
});

// 获取源页面URL
function getSrcPage(text) {
  return 'https://dict.youdao.com/w/' + encodeURIComponent(text.replace(/\s+/g, ' '));
}

// 主翻译函数
async function handleTranslate(word) {
  const url = getSrcPage(word.trim());

  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();
    return await parseYoudaoResult(html);
  } catch (error) {
    throw new Error('网络请求失败: ' + error.message);
  }
}

function parseWithRegex(html) {
  const result = {
    word: '',
    phonetics: [],
    translations: [],
    examples: []
  };

  // 提取单词
  const wordMatch = html.match(/<span class="keyword">([^<]+)<\/span>/);
  if (wordMatch) {
    result.word = wordMatch[1].trim();
  }

  // 提取音标和发音链接
  const pronounceRegex = /<div class="pronounce"[^>]*>[\s\S]*?<span>([^<]+)<\/span>[\s\S]*?<span class="phonetic">([^<]+)<\/span>/g;
  let match;
  while ((match = pronounceRegex.exec(html)) !== null) {
    const type = match[1].trim(); // 英 或 美
    const phonetic = match[2].trim();
    
    // 查找音频链接
    const audioMatch = html.match(new RegExp(`data-rel="([^"]+)"[^>]*>[\\s\\S]*?${type}`, 'i'));
    const audioUrl = audioMatch ? `https://dict.youdao.com/dictvoice?audio=${audioMatch[1]}` : null;
    
    result.phonetics.push({
      type: type,
      text: phonetic,
      audio: audioUrl
    });
  }

  // 提取基本释义 - 改进的正则
  const transMatch = html.match(/<div class="trans-container"[^>]*>[\s\S]*?<ul>([\s\S]*?)<\/ul>/);
  if (transMatch) {
    const transHTML = transMatch[1];
    const liRegex = /<li>([^<]+(?:<[^>]+>[^<]+)*[^<]+)<\/li>/g;
    while ((match = liRegex.exec(transHTML)) !== null) {
      // 清理HTML标签
      const text = match[1].replace(/<[^>]+>/g, '').trim();
      if (text) {
        result.translations.push(text);
      }
    }
  }

  // 如果没有找到基本释义，尝试查找其他格式
  if (result.translations.length === 0) {
    // 尝试匹配其他可能的释义格式
    const altTransRegex = /<div class="trans-container"[^>]*>([\s\S]*?)<\/div>/;
    const altMatch = html.match(altTransRegex);
    if (altMatch) {
      const content = altMatch[1];
      // 提取所有文本内容，移除HTML标签
      const cleanText = content.replace(/<[^>]+>/g, ' ').replace(/\s+/g, ' ').trim();
      if (cleanText && cleanText.length > 0) {
        result.translations.push(cleanText);
      }
    }
  }

  // 提取例句（简化版）
  const exampleRegex = /<div class="examples"[^>]*>[\s\S]*?<p[^>]*>([^<]+)<\/p>[\s\S]*?<p[^>]*>([^<]+)<\/p>/g;
  let exampleCount = 0;
  while ((match = exampleRegex.exec(html)) !== null && exampleCount < 3) {
    result.examples.push({
      en: match[1].trim(),
      cn: match[2].trim()
    });
    exampleCount++;
  }

  return result;
}

