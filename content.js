// content.js - OmniWord划词翻译内容脚本
let selectionPopup = null;
let translatePopup = null;
let isTranslating = false;

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 监听鼠标选择文本
const handleSelection = debounce(function(e) {
  const selectedText = window.getSelection().toString().trim();

  // 改进的文本检测：支持中英文混合，但排除过长的文本
  if (selectedText &&
      selectedText.length > 0 &&
      selectedText.length < 100 &&
      !isTranslating &&
      // 排除纯数字、纯符号等无意义文本
      /[a-zA-Z\u4e00-\u9fa5]/.test(selectedText)) {
    showTranslateButton(e.pageX, e.pageY, selectedText);
  } else {
    hideTranslateButton();
  }
}, 200);

document.addEventListener('mouseup', handleSelection);
document.addEventListener('selectionchange', function() {
  if (!window.getSelection().toString().trim()) {
    hideTranslateButton();
    hideTranslatePopup();
  }
});

function showTranslateButton(x, y, text) {
  hideTranslateButton();

  const button = document.createElement('div');
  button.className = 'omniword-translate-button';
  button.innerHTML = `
    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
      <path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>
    </svg>
  `;

  // 计算按钮位置，确保不超出屏幕边界
  const windowWidth = window.innerWidth + window.scrollX;
  const windowHeight = window.innerHeight + window.scrollY;
  let leftPos = x + 10;
  let topPos = y - 45;

  // 防止超出右边界
  if (leftPos + 40 > windowWidth) {
    leftPos = x - 50;
  }

  // 防止超出上边界
  if (topPos < window.scrollY) {
    topPos = y + 20;
  }

  button.style.left = leftPos + 'px';
  button.style.top = topPos + 'px';

  button.addEventListener('click', function(e) {
    e.stopPropagation();
    translateSelection(text, leftPos, topPos);
  });

  // 添加键盘支持
  button.setAttribute('tabindex', '0');
  button.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      translateSelection(text, leftPos, topPos);
    }
  });

  document.body.appendChild(button);
  selectionPopup = button;

  // 自动聚焦到按钮，方便键盘操作
  setTimeout(() => button.focus(), 100);
}

function hideTranslateButton() {
  if (selectionPopup) {
    selectionPopup.remove();
    selectionPopup = null;
  }
}

function hideTranslatePopup() {
  if (translatePopup) {
    translatePopup.remove();
    translatePopup = null;
  }
}

function translateSelection(text, x, y) {
  if (isTranslating) return;

  hideTranslateButton();
  hideTranslatePopup();
  isTranslating = true;

  // 创建翻译弹窗
  translatePopup = createTranslatePopup(createLoadingHTML(), x, y);

  // 发送翻译请求
  chrome.runtime.sendMessage(
    { action: 'translate', word: text },
    function(response) {
      isTranslating = false;
      if (response && response.success) {
        const content = formatTranslateResult(response.data);
        updateTranslatePopup(translatePopup, content);
      } else {
        const errorContent = `
          <div class="omniword-error">
            翻译失败: ${escapeHtml(response?.error || '未知错误')}
          </div>
        `;
        updateTranslatePopup(translatePopup, errorContent);
      }
    }
  );
}

// 创建加载HTML
function createLoadingHTML() {
  return `
    <div class="omniword-loading">
      <div class="omniword-loading-spinner"></div>
      <span>正在翻译...</span>
    </div>
  `;
}

function createTranslatePopup(content, x, y) {
  const popup = document.createElement('div');
  popup.className = 'omniword-translate-popup';

  // 调整位置，确保不超出屏幕
  const windowWidth = window.innerWidth + window.scrollX;
  const windowHeight = window.innerHeight + window.scrollY;
  let leftPos = x;
  let topPos = y + 20;

  // 如果超出右边界
  if (leftPos + 450 > windowWidth) {
    leftPos = windowWidth - 470;
  }

  // 如果超出下边界
  if (topPos + 350 > windowHeight) {
    topPos = y - 370;
  }

  // 确保不超出左边界和上边界
  if (leftPos < window.scrollX + 10) {
    leftPos = window.scrollX + 10;
  }
  if (topPos < window.scrollY + 10) {
    topPos = window.scrollY + 10;
  }

  popup.style.left = leftPos + 'px';
  popup.style.top = topPos + 'px';
  popup.innerHTML = content;
  
  // 添加关闭按钮
  const closeBtn = document.createElement('div');
  closeBtn.className = 'omniword-close-btn';
  closeBtn.innerHTML = '×';
  closeBtn.addEventListener('click', hideTranslatePopup);
  closeBtn.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      hideTranslatePopup();
    }
  });
  closeBtn.setAttribute('tabindex', '0');
  closeBtn.setAttribute('aria-label', '关闭翻译窗口');
  popup.appendChild(closeBtn);
  
  // 点击其他地方关闭
  setTimeout(() => {
    document.addEventListener('click', function closePopup(e) {
      if (!popup.contains(e.target)) {
        hideTranslatePopup();
        document.removeEventListener('click', closePopup);
      }
    });
  }, 100);
  
  document.body.appendChild(popup);
  return popup;
}

function updateTranslatePopup(popup, content) {
  // 保留关闭按钮
  const closeBtn = popup.querySelector('div[style*="position: absolute"]');
  popup.innerHTML = content;
  if (closeBtn) {
    popup.appendChild(closeBtn);
  }
}

function formatTranslateResult(data) {
  let html = `<div style="padding-right: 20px;">`;
  
  // 单词
  if (data.word) {
    html += `<h3 style="margin: 0 0 12px 0; color: #1976d2; font-size: 20px;">${data.word}</h3>`;
  }
  
  // 音标
  if (data.phonetics && data.phonetics.length > 0) {
    html += `<div style="margin-bottom: 12px;">`;
    data.phonetics.forEach(p => {
      if (typeof p === 'object') {
        html += `
          <div style="display: inline-flex; align-items: center; margin-right: 15px;">
            <span style="color: #666; margin-right: 5px;">${p.type}</span>
            <span style="color: #333;">${p.text}</span>
            ${p.audio ? `
              <button onclick="playAudio('${p.audio}')" style="
                background: none;
                border: none;
                cursor: pointer;
                padding: 2px;
                margin-left: 5px;
              ">🔊</button>
            ` : ''}
          </div>
        `;
      } else {
        html += `<span style="margin-right: 15px; color: #666;">${p}</span>`;
      }
    });
    html += `</div>`;
  }
  
  // 释义
  if (data.translations && data.translations.length > 0) {
    html += `<div style="border-top: 1px solid #eee; padding-top: 12px;">`;
    data.translations.forEach(t => {
      html += `<div style="margin: 8px 0; color: #333; line-height: 1.5;">${t}</div>`;
    });
    html += `</div>`;
  }
  
  // 例句
  if (data.examples && data.examples.length > 0) {
    html += `
      <div style="border-top: 1px solid #eee; margin-top: 12px; padding-top: 12px;">
        <div style="color: #666; font-size: 14px; margin-bottom: 8px;">例句：</div>
    `;
    data.examples.forEach((ex, index) => {
      html += `
        <div style="margin-bottom: 10px; padding: 8px; background: #f5f5f5; border-radius: 4px;">
          <div style="color: #333; margin-bottom: 4px;">${index + 1}. ${ex.en}</div>
          <div style="color: #666; font-size: 14px;">${ex.cn}</div>
        </div>
      `;
    });
    html += `</div>`;
  }
  
  html += `</div>`;
  
  // 添加播放音频的函数
  html += `
    <script>
      function playAudio(url) {
        const audio = new Audio(url);
        audio.play();
      }
    </script>
  `;
  
  return html;
}

// 添加样式
const style = document.createElement('style');
style.textContent = `
  @keyframes fadeIn {
    from { 
      opacity: 0; 
      transform: translateY(-10px); 
    }
    to { 
      opacity: 1; 
      transform: translateY(0); 
    }
  }
  
  .youdao-translate-popup * {
    box-sizing: border-box;
  }
`;
document.head.appendChild(style);